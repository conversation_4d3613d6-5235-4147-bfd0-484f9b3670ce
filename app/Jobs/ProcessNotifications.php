<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Log;

class ProcessNotifications implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(NotificationService $notificationService): void
    {
        try {
            $processed = $notificationService->processPending();

            Log::info("ProcessNotifications job completed", [
                'processed_count' => $processed,
                'timestamp' => now(),
            ]);

        } catch (\Exception $e) {
            Log::error("ProcessNotifications job failed", [
                'error' => $e->getMessage(),
                'timestamp' => now(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ProcessNotifications job failed permanently", [
            'error' => $exception->getMessage(),
            'timestamp' => now(),
        ]);
    }
}
