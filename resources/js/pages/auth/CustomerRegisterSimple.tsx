import { Head, Link, useForm } from '@inertiajs/react';
import { LoaderCircle, Eye, EyeOff, CheckCircle, ArrowRight, Truck, Shield, User, Building, Mail, Phone, MapPin, ArrowLeft } from 'lucide-react';
import { FormEventHandler, useState } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

type CustomerRegisterForm = {
    first_name: string;
    last_name: string;
    email: string;
    password: string;
    password_confirmation: string;
    phone: string;
    company_name: string;
    industry: string;
    company_size: string;
    address_line_1: string;
    city: string;
    state_province: string;
    postal_code: string;
    country: string;
    monthly_volume: string;
    primary_service: string;
    hear_about_us: string;
    terms_accepted: boolean;
    marketing_emails: boolean;
};

interface CustomerRegisterProps {
    countries?: Record<string, string>;
    industries?: Record<string, string>;
}

export default function CustomerRegisterSimple({ countries = {}, industries = {} }: CustomerRegisterProps) {
    const [showPassword, setShowPassword] = useState(false);
    const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false);
    const [currentStep, setCurrentStep] = useState(1);

    const { data, setData, post, processing, errors, reset } = useForm<CustomerRegisterForm>({
        first_name: '',
        last_name: '',
        email: '',
        password: '',
        password_confirmation: '',
        phone: '',
        company_name: '',
        industry: '',
        company_size: '',
        address_line_1: '',
        city: '',
        state_province: '',
        postal_code: '',
        country: '',
        monthly_volume: '',
        primary_service: '',
        hear_about_us: '',
        terms_accepted: false,
        marketing_emails: false,
    });

    const validateStep1 = () => {
        const step1Fields = ['first_name', 'last_name', 'email', 'phone', 'company_name', 'industry', 'company_size'];
        return step1Fields.every(field => data[field as keyof CustomerRegisterForm]);
    };

    const nextStep = () => {
        if (currentStep === 1 && validateStep1()) {
            setCurrentStep(2);
        }
    };

    const prevStep = () => {
        if (currentStep === 2) {
            setCurrentStep(1);
        }
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('customer.register.store'), {
            onFinish: () => reset('password', 'password_confirmation'),
        });
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
            <Head title="Create Account - RT Express" />
            
            <div className="flex min-h-screen">
                {/* Left Side - Branding */}
                <div className="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-center lg:px-12 xl:px-16 bg-gradient-to-br from-blue-600 to-indigo-700 relative overflow-hidden">
                    {/* Background Pattern */}
                    <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
                    
                    <div className="relative z-10">
                        {/* Logo */}
                        <div className="flex items-center space-x-3 mb-12">
                            <div className="bg-white/20 p-4 rounded-2xl backdrop-blur-sm">
                                <Truck className="h-10 w-10 text-white" />
                            </div>
                            <div>
                                <h1 className="text-3xl font-bold text-white">RT Express</h1>
                                <p className="text-blue-100">Global Shipping Solutions</p>
                            </div>
                        </div>

                        {/* Features */}
                        <div className="space-y-8">
                            <div className="flex items-start space-x-4">
                                <div className="bg-white/20 p-3 rounded-xl backdrop-blur-sm">
                                    <CheckCircle className="h-6 w-6 text-white" />
                                </div>
                                <div>
                                    <h3 className="text-xl font-semibold text-white mb-2">
                                        Real-time Tracking
                                    </h3>
                                    <p className="text-blue-100">
                                        Track your shipments in real-time with detailed updates and notifications.
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-4">
                                <div className="bg-white/20 p-3 rounded-xl backdrop-blur-sm">
                                    <Shield className="h-6 w-6 text-white" />
                                </div>
                                <div>
                                    <h3 className="text-xl font-semibold text-white mb-2">
                                        Secure & Reliable
                                    </h3>
                                    <p className="text-blue-100">
                                        Your shipments are protected with our comprehensive insurance and security measures.
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-4">
                                <div className="bg-white/20 p-3 rounded-xl backdrop-blur-sm">
                                    <Truck className="h-6 w-6 text-white" />
                                </div>
                                <div>
                                    <h3 className="text-xl font-semibold text-white mb-2">
                                        Global Network
                                    </h3>
                                    <p className="text-blue-100">
                                        Ship anywhere in the world with our extensive logistics network.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Right Side - Registration Form */}
                <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
                    <div className="w-full max-w-lg space-y-8">
                        {/* Header */}
                        <div className="text-center">
                            <div className="lg:hidden mb-8">
                                <div className="flex items-center justify-center space-x-3 mb-4">
                                    <div className="bg-blue-600 p-3 rounded-xl">
                                        <Truck className="h-8 w-8 text-white" />
                                    </div>
                                    <div className="text-left">
                                        <h1 className="text-2xl font-bold text-gray-900">RT Express</h1>
                                        <p className="text-gray-600 text-sm">Global Shipping Solutions</p>
                                    </div>
                                </div>
                            </div>

                            <h2 className="text-3xl font-bold text-gray-900 mb-2">
                                Create your account
                            </h2>
                            <p className="text-gray-600 mb-4">
                                {currentStep === 1
                                    ? "Let's start with your basic information"
                                    : "Complete your account setup"
                                }
                            </p>

                            {/* Step Indicator */}
                            <div className="flex items-center justify-center space-x-4 mb-6">
                                <div className="flex items-center">
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                                        currentStep >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
                                    }`}>
                                        1
                                    </div>
                                    <span className={`ml-2 text-sm ${currentStep >= 1 ? 'text-blue-600' : 'text-gray-500'}`}>
                                        Basic Info
                                    </span>
                                </div>
                                <div className={`w-8 h-0.5 ${currentStep >= 2 ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
                                <div className="flex items-center">
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                                        currentStep >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
                                    }`}>
                                        2
                                    </div>
                                    <span className={`ml-2 text-sm ${currentStep >= 2 ? 'text-blue-600' : 'text-gray-500'}`}>
                                        Complete Setup
                                    </span>
                                </div>
                            </div>
                        </div>

                        {/* Registration Form */}
                        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                            <CardContent className="p-8">
                                <form className="space-y-6" onSubmit={submit}>
                                    {currentStep === 1 && (
                                        <div className="space-y-5">
                                            {/* Step 1: Personal Information */}
                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <Label htmlFor="first_name" className="text-sm font-medium text-gray-700">
                                                        First Name
                                                    </Label>
                                                    <Input
                                                        id="first_name"
                                                        type="text"
                                                        required
                                                        autoFocus
                                                        value={data.first_name}
                                                        onChange={(e) => setData('first_name', e.target.value)}
                                                        className={`mt-1 h-12 ${errors.first_name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}
                                                        placeholder="John"
                                                    />
                                                    <InputError message={errors.first_name} />
                                                </div>

                                                <div>
                                                    <Label htmlFor="last_name" className="text-sm font-medium text-gray-700">
                                                        Last Name
                                                    </Label>
                                                    <Input
                                                        id="last_name"
                                                        type="text"
                                                        required
                                                        value={data.last_name}
                                                        onChange={(e) => setData('last_name', e.target.value)}
                                                        className={`mt-1 h-12 ${errors.last_name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}
                                                        placeholder="Doe"
                                                    />
                                                    <InputError message={errors.last_name} />
                                                </div>
                                            </div>

                                            <div>
                                                <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                                                    Email address
                                                </Label>
                                                <Input
                                                    id="email"
                                                    type="email"
                                                    required
                                                    value={data.email}
                                                    onChange={(e) => setData('email', e.target.value)}
                                                    className={`mt-1 h-12 ${errors.email ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}
                                                    placeholder="<EMAIL>"
                                                />
                                                <InputError message={errors.email} />
                                            </div>

                                            <div>
                                                <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                                                    Phone Number
                                                </Label>
                                                <Input
                                                    id="phone"
                                                    type="tel"
                                                    required
                                                    value={data.phone}
                                                    onChange={(e) => setData('phone', e.target.value)}
                                                    className={`mt-1 h-12 ${errors.phone ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}
                                                    placeholder="+****************"
                                                />
                                                <InputError message={errors.phone} />
                                            </div>

                                            <div>
                                                <Label htmlFor="company_name" className="text-sm font-medium text-gray-700">
                                                    Company Name
                                                </Label>
                                                <Input
                                                    id="company_name"
                                                    type="text"
                                                    required
                                                    value={data.company_name}
                                                    onChange={(e) => setData('company_name', e.target.value)}
                                                    className={`mt-1 h-12 ${errors.company_name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}
                                                    placeholder="Your Company Inc."
                                                />
                                                <InputError message={errors.company_name} />
                                            </div>

                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <Label htmlFor="industry" className="text-sm font-medium text-gray-700">
                                                        Industry
                                                    </Label>
                                                    <Select value={data.industry} onValueChange={(value) => setData('industry', value)}>
                                                        <SelectTrigger className={`mt-1 h-12 ${errors.industry ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}>
                                                            <SelectValue placeholder="Select industry" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="technology">Technology</SelectItem>
                                                            <SelectItem value="manufacturing">Manufacturing</SelectItem>
                                                            <SelectItem value="retail">Retail</SelectItem>
                                                            <SelectItem value="healthcare">Healthcare</SelectItem>
                                                            <SelectItem value="finance">Finance</SelectItem>
                                                            <SelectItem value="other">Other</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <InputError message={errors.industry} />
                                                </div>

                                                <div>
                                                    <Label htmlFor="company_size" className="text-sm font-medium text-gray-700">
                                                        Company Size
                                                    </Label>
                                                    <Select value={data.company_size} onValueChange={(value) => setData('company_size', value)}>
                                                        <SelectTrigger className={`mt-1 h-12 ${errors.company_size ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}>
                                                            <SelectValue placeholder="Select size" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="1-10">1-10 employees</SelectItem>
                                                            <SelectItem value="11-50">11-50 employees</SelectItem>
                                                            <SelectItem value="51-200">51-200 employees</SelectItem>
                                                            <SelectItem value="201-1000">201-1000 employees</SelectItem>
                                                            <SelectItem value="1000+">1000+ employees</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <InputError message={errors.company_size} />
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Step 1 Navigation */}
                                    {currentStep === 1 && (
                                        <Button
                                            type="button"
                                            onClick={nextStep}
                                            disabled={!validateStep1()}
                                            className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-all duration-200"
                                        >
                                            Continue
                                            <ArrowRight className="ml-2 h-5 w-5" />
                                        </Button>
                                    )}

                                    {currentStep === 2 && (
                                        <div className="space-y-5">
                                            {/* Step 2: Address Information */}
                                            <div>
                                                <Label htmlFor="address_line_1" className="text-sm font-medium text-gray-700">
                                                    Address
                                                </Label>
                                                <Input
                                                    id="address_line_1"
                                                    type="text"
                                                    required
                                                    value={data.address_line_1}
                                                    onChange={(e) => setData('address_line_1', e.target.value)}
                                                    className={`mt-1 h-12 ${errors.address_line_1 ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}
                                                    placeholder="123 Main Street"
                                                />
                                                <InputError message={errors.address_line_1} />
                                            </div>

                                            <div className="grid grid-cols-3 gap-4">
                                                <div>
                                                    <Label htmlFor="city" className="text-sm font-medium text-gray-700">
                                                        City
                                                    </Label>
                                                    <Input
                                                        id="city"
                                                        type="text"
                                                        required
                                                        value={data.city}
                                                        onChange={(e) => setData('city', e.target.value)}
                                                        className={`mt-1 h-12 ${errors.city ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}
                                                        placeholder="New York"
                                                    />
                                                    <InputError message={errors.city} />
                                                </div>

                                                <div>
                                                    <Label htmlFor="state_province" className="text-sm font-medium text-gray-700">
                                                        State
                                                    </Label>
                                                    <Input
                                                        id="state_province"
                                                        type="text"
                                                        required
                                                        value={data.state_province}
                                                        onChange={(e) => setData('state_province', e.target.value)}
                                                        className={`mt-1 h-12 ${errors.state_province ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}
                                                        placeholder="NY"
                                                    />
                                                    <InputError message={errors.state_province} />
                                                </div>

                                                <div>
                                                    <Label htmlFor="postal_code" className="text-sm font-medium text-gray-700">
                                                        ZIP Code
                                                    </Label>
                                                    <Input
                                                        id="postal_code"
                                                        type="text"
                                                        required
                                                        value={data.postal_code}
                                                        onChange={(e) => setData('postal_code', e.target.value)}
                                                        className={`mt-1 h-12 ${errors.postal_code ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}
                                                        placeholder="10001"
                                                    />
                                                    <InputError message={errors.postal_code} />
                                                </div>
                                            </div>

                                            <div>
                                                <Label htmlFor="country" className="text-sm font-medium text-gray-700">
                                                    Country
                                                </Label>
                                                <Select value={data.country} onValueChange={(value) => setData('country', value)}>
                                                    <SelectTrigger className={`mt-1 h-12 ${errors.country ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}>
                                                        <SelectValue placeholder="Select country" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="US">United States</SelectItem>
                                                        <SelectItem value="CA">Canada</SelectItem>
                                                        <SelectItem value="GB">United Kingdom</SelectItem>
                                                        <SelectItem value="AU">Australia</SelectItem>
                                                        <SelectItem value="DE">Germany</SelectItem>
                                                        <SelectItem value="FR">France</SelectItem>
                                                        <SelectItem value="JP">Japan</SelectItem>
                                                        <SelectItem value="CN">China</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                <InputError message={errors.country} />
                                            </div>

                                            {/* Password Fields */}
                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                                                        Password
                                                    </Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="password"
                                                            type={showPassword ? 'text' : 'password'}
                                                            required
                                                            value={data.password}
                                                            onChange={(e) => setData('password', e.target.value)}
                                                            className={`mt-1 h-12 pr-12 ${errors.password ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}
                                                            placeholder="Create password"
                                                        />
                                                        <button
                                                            type="button"
                                                            className="absolute inset-y-0 right-0 flex items-center pr-3 mt-1"
                                                            onClick={() => setShowPassword(!showPassword)}
                                                        >
                                                            {showPassword ? (
                                                                <EyeOff className="h-5 w-5 text-gray-400" />
                                                            ) : (
                                                                <Eye className="h-5 w-5 text-gray-400" />
                                                            )}
                                                        </button>
                                                    </div>
                                                    <InputError message={errors.password} />
                                                </div>

                                                <div>
                                                    <Label htmlFor="password_confirmation" className="text-sm font-medium text-gray-700">
                                                        Confirm Password
                                                    </Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="password_confirmation"
                                                            type={showPasswordConfirmation ? 'text' : 'password'}
                                                            required
                                                            value={data.password_confirmation}
                                                            onChange={(e) => setData('password_confirmation', e.target.value)}
                                                            className={`mt-1 h-12 pr-12 ${errors.password_confirmation ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}
                                                            placeholder="Confirm password"
                                                        />
                                                        <button
                                                            type="button"
                                                            className="absolute inset-y-0 right-0 flex items-center pr-3 mt-1"
                                                            onClick={() => setShowPasswordConfirmation(!showPasswordConfirmation)}
                                                        >
                                                            {showPasswordConfirmation ? (
                                                                <EyeOff className="h-5 w-5 text-gray-400" />
                                                            ) : (
                                                                <Eye className="h-5 w-5 text-gray-400" />
                                                            )}
                                                        </button>
                                                    </div>
                                                    <InputError message={errors.password_confirmation} />
                                                </div>
                                            </div>

                                            {/* Shipping Preferences */}
                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <Label htmlFor="monthly_volume" className="text-sm font-medium text-gray-700">
                                                        Monthly Shipping Volume
                                                    </Label>
                                                    <Select value={data.monthly_volume} onValueChange={(value) => setData('monthly_volume', value)}>
                                                        <SelectTrigger className={`mt-1 h-12 ${errors.monthly_volume ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}>
                                                            <SelectValue placeholder="Select volume" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="1-10">1-10 shipments</SelectItem>
                                                            <SelectItem value="11-50">11-50 shipments</SelectItem>
                                                            <SelectItem value="51-200">51-200 shipments</SelectItem>
                                                            <SelectItem value="201-500">201-500 shipments</SelectItem>
                                                            <SelectItem value="500+">500+ shipments</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <InputError message={errors.monthly_volume} />
                                                </div>

                                                <div>
                                                    <Label htmlFor="primary_service" className="text-sm font-medium text-gray-700">
                                                        Primary Service
                                                    </Label>
                                                    <Select value={data.primary_service} onValueChange={(value) => setData('primary_service', value)}>
                                                        <SelectTrigger className={`mt-1 h-12 ${errors.primary_service ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}>
                                                            <SelectValue placeholder="Select service" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="domestic">Domestic Shipping</SelectItem>
                                                            <SelectItem value="international">International Shipping</SelectItem>
                                                            <SelectItem value="both">Both Domestic & International</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <InputError message={errors.primary_service} />
                                                </div>
                                            </div>

                                            {/* How did you hear about us */}
                                            <div>
                                                <Label htmlFor="hear_about_us" className="text-sm font-medium text-gray-700">
                                                    How did you hear about us?
                                                </Label>
                                                <Select value={data.hear_about_us} onValueChange={(value) => setData('hear_about_us', value)}>
                                                    <SelectTrigger className={`mt-1 h-12 ${errors.hear_about_us ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}>
                                                        <SelectValue placeholder="Select option" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="search_engine">Search Engine</SelectItem>
                                                        <SelectItem value="social_media">Social Media</SelectItem>
                                                        <SelectItem value="referral">Referral</SelectItem>
                                                        <SelectItem value="advertisement">Advertisement</SelectItem>
                                                        <SelectItem value="trade_show">Trade Show</SelectItem>
                                                        <SelectItem value="other">Other</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                                <InputError message={errors.hear_about_us} />
                                            </div>

                                            {/* Terms and Marketing */}
                                            <div className="space-y-4">
                                                <div className="flex items-start">
                                                    <Checkbox
                                                        id="terms_accepted"
                                                        checked={data.terms_accepted}
                                                        onCheckedChange={(checked) => setData('terms_accepted', !!checked)}
                                                        className="mt-1"
                                                    />
                                                    <Label htmlFor="terms_accepted" className="ml-3 text-sm text-gray-700">
                                                        I agree to the{' '}
                                                        <Link href="#" className="text-blue-600 hover:text-blue-800 font-medium">
                                                            Terms of Service
                                                        </Link>{' '}
                                                        and{' '}
                                                        <Link href="#" className="text-blue-600 hover:text-blue-800 font-medium">
                                                            Privacy Policy
                                                        </Link>
                                                    </Label>
                                                </div>
                                                <InputError message={errors.terms_accepted} />

                                                <div className="flex items-start">
                                                    <Checkbox
                                                        id="marketing_emails"
                                                        checked={data.marketing_emails}
                                                        onCheckedChange={(checked) => setData('marketing_emails', !!checked)}
                                                        className="mt-1"
                                                    />
                                                    <Label htmlFor="marketing_emails" className="ml-3 text-sm text-gray-700">
                                                        I would like to receive marketing emails about new features and promotions
                                                    </Label>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Step 2 Navigation */}
                                    {currentStep === 2 && (
                                        <div className="flex space-x-4">
                                            <Button
                                                type="button"
                                                onClick={prevStep}
                                                variant="outline"
                                                className="flex-1 h-12 border-gray-300 text-gray-700 hover:bg-gray-50"
                                            >
                                                <ArrowLeft className="mr-2 h-5 w-5" />
                                                Back
                                            </Button>
                                            <Button
                                                type="submit"
                                                disabled={processing}
                                                className="flex-1 h-12 bg-rt-red hover:bg-rt-red-700 text-white font-medium shadow-rt-red hover:shadow-rt-red-lg transition-all duration-200"
                                            >
                                                {processing ? (
                                                    <>
                                                        <LoaderCircle className="mr-2 h-5 w-5 animate-spin" />
                                                        Creating account...
                                                    </>
                                                ) : (
                                                    <>
                                                        Create account
                                                        <CheckCircle className="ml-2 h-5 w-5" />
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    )}
                                </form>
                            </CardContent>
                        </Card>

                        {/* Login Link */}
                        <div className="text-center">
                            <p className="text-gray-600">
                                Already have an account?{' '}
                                <Link href={route('login')} className="text-blue-600 hover:text-blue-800 font-medium">
                                    Sign in
                                </Link>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
